import { config, clientConfig } from './config'

/**
 * Generic API response type from Strapi
 */
export interface StrapiResponse<T = any> {
  data: T
  meta: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

/**
 * Strapi data attributes with common fields
 */
export interface StrapiData {
  id: number
  documentId: string
  createdAt: string
  updatedAt: string
  publishedAt: string
}

/**
 * API Error type
 */
export interface ApiError {
  message: string
  status?: number
  details?: any
}

/**
 * Generic function to fetch data from Strapi API via Next.js API routes
 * @param endpoint - The API endpoint (e.g., '/api/hero?populate=*')
 * @param options - Additional fetch options
 * @returns Promise with the API response
 */
export async function fetchFromStrapi<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<StrapiResponse<T>> {
  // Remove '/api/' prefix if present and use our API route
  const cleanEndpoint = endpoint.startsWith('/api/') ? endpoint.slice(5) : endpoint
  const url = `/api/strapi/${cleanEndpoint}`

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  }

  const fetchOptions: RequestInit = {
    method: 'GET',
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, fetchOptions)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        `API request failed: ${response.status} ${response.statusText}. ${
          errorData.error?.message || errorData.message || ''
        }`
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}

/**
 * Direct function to fetch data from Strapi API (server-side only)
 * @param endpoint - The API endpoint (e.g., '/api/hero?populate=*')
 * @param options - Additional fetch options
 * @returns Promise with the API response
 */
export async function fetchFromStrapiDirect<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<StrapiResponse<T>> {
  // This function should only be used server-side
  if (typeof window !== 'undefined') {
    throw new Error('fetchFromStrapiDirect can only be used server-side')
  }

  const url = `${config.strapi.url}${endpoint}`

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  }

  // Add authorization header if token is available
  if (config.strapi.apiToken) {
    defaultHeaders.Authorization = `Bearer ${config.strapi.apiToken}`
  }

  const fetchOptions: RequestInit = {
    method: 'GET',
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, fetchOptions)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        `API request failed: ${response.status} ${response.statusText}. ${
          errorData.error?.message || ''
        }`
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}

/**
 * Fetch data with error handling and loading states
 * Useful for client-side data fetching with React hooks
 */
export async function fetchWithErrorHandling<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<{ data: T | null; error: ApiError | null; loading: boolean }> {
  try {
    const response = await fetchFromStrapi<T>(endpoint, options)
    return {
      data: response.data,
      error: null,
      loading: false,
    }
  } catch (error) {
    const apiError: ApiError = {
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      details: error,
    }
    
    return {
      data: null,
      error: apiError,
      loading: false,
    }
  }
}

/**
 * Server-side data fetching with caching
 * Use this in Next.js server components or API routes
 */
export async function fetchServerSide<T = any>(
  endpoint: string,
  options: RequestInit & { revalidate?: number } = {}
): Promise<T | null> {
  const { revalidate, ...fetchOptions } = options

  try {
    const response = await fetchFromStrapiDirect<T>(endpoint, {
      ...fetchOptions,
      next: {
        revalidate: revalidate || 60, // Default 1 minute cache
      },
    })

    return response.data
  } catch (error) {
    console.error('Server-side fetch error:', error)
    return null
  }
}
