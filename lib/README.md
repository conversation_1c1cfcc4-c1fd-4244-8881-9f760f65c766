# Strapi API Integration

This directory contains the configuration and utilities for integrating with the Strapi backend.

## Files Overview

- `config.ts` - Configuration settings and API endpoints
- `api.ts` - Core API functions for making requests to Strapi
- `types.ts` - TypeScript types for Strapi data structures
- `hooks/useHeroData.ts` - React hook for fetching hero section data
- `hooks/useStrapiData.ts` - Generic React hooks for fetching any Strapi data

## Setup

1. **Environment Variables**: Make sure you have the following in your `.env.local`:
   ```
   NEXT_PUBLIC_STRAPI_URL=http://localhost:1337
   STRAPI_API_TOKEN=your_strapi_api_token_here
   ```

2. **Configuration**: Update `config.ts` to add new endpoints as needed.

## Usage Examples

### 1. Using the Hero Data Hook

```tsx
import { useHeroData } from '@/lib/hooks/useHeroData'

function HeroComponent() {
  const { data, loading, error } = useHeroData()

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      <h1>{data?.title}</h1>
      <p>{extractTextFromRichText(data?.subtitle || [])}</p>
    </div>
  )
}
```

### 2. Using the Generic Strapi Data Hook

```tsx
import { useStrapiData } from '@/lib/hooks/useStrapiData'

function AboutComponent() {
  const { data, loading, error } = useStrapiData('/api/about?populate=*')

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return <div>{/* Render your about data */}</div>
}
```

### 3. Fetching Multiple Endpoints

```tsx
import { useMultipleStrapiData } from '@/lib/hooks/useStrapiData'

function HomePage() {
  const { data, loading, error } = useMultipleStrapiData({
    hero: '/api/hero?populate=*',
    about: '/api/about?populate=*',
    services: '/api/services?populate=*'
  })

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      {/* Use data.hero, data.about, data.services */}
    </div>
  )
}
```

### 4. Server-Side Data Fetching

```tsx
import { fetchServerSide } from '@/lib/api'

export default async function ServerPage() {
  const heroData = await fetchServerSide('/api/hero?populate=*', {
    revalidate: 300 // Cache for 5 minutes
  })

  return (
    <div>
      <h1>{heroData?.title}</h1>
    </div>
  )
}
```

### 5. Direct API Calls

```tsx
import { fetchFromStrapi } from '@/lib/api'

async function handleSubmit() {
  try {
    const response = await fetchFromStrapi('/api/contact', {
      method: 'POST',
      body: JSON.stringify(formData)
    })
    console.log('Success:', response.data)
  } catch (error) {
    console.error('Error:', error)
  }
}
```

## Adding New Endpoints

1. **Add to config.ts**:
   ```ts
   export const endpoints = {
     hero: '/api/hero?populate=*',
     about: '/api/about?populate=*',
     newSection: '/api/new-section?populate=*', // Add new endpoint
   } as const
   ```

2. **Create types in types.ts**:
   ```ts
   export interface NewSectionData extends StrapiData {
     title: string
     content: string
     // Add other fields
   }
   ```

3. **Create a specific hook (optional)**:
   ```ts
   export function useNewSectionData() {
     return useStrapiData<NewSectionData>(endpoints.newSection)
   }
   ```

## Error Handling

All hooks and functions include comprehensive error handling:
- Network errors
- API errors (4xx, 5xx responses)
- JSON parsing errors
- Loading states

## Caching

- Client-side hooks automatically cache data until component unmounts
- Server-side functions use Next.js caching with configurable revalidation
- Use the `refetch` function to manually refresh data

## TypeScript Support

All functions and hooks are fully typed with TypeScript for better development experience and type safety.
