import { StrapiData } from './api'

/**
 * Rich text content type from Strapi
 */
export interface RichTextContent {
  type: string
  children: Array<{
    type: string
    text: string
  }>
}

/**
 * Hero feature item
 */
export interface HeroFeature {
  id: number
  title: string
  description: string
}

/**
 * Hero section data from Strapi
 */
export interface HeroData extends StrapiData {
  title: string
  subtitle: RichTextContent[]
  ctaPrimaryText: string
  ctaPrimaryLink: string
  ctaSecondaryText: string
  ctaSecondaryLink: string
  secondTitle: string
  features: HeroFeature[]
}

/**
 * Utility function to extract plain text from rich text content
 */
export function extractTextFromRichText(richText: RichTextContent[]): string {
  return richText
    .map(block => 
      block.children
        .map(child => child.text)
        .join('')
    )
    .join(' ')
}
