import { StrapiData } from './api'

/**
 * Rich text content type from Strapi
 */
export interface RichTextContent {
  type: string
  children: Array<{
    type: string
    text: string
  }>
}

/**
 * Hero feature item
 */
export interface HeroFeature {
  id: number
  title: string
  description: string
  icon: string
}


/**
 * Hero feature item
 */
export interface HeroStats {
  id: number
  label: string
  value: string
}

/**
 * Hero section data from Strapi
 */
export interface HeroData extends StrapiData {
  title: string
  subtitle: RichTextContent[]
  ctaPrimaryText: string
  ctaPrimaryLink: string
  ctaSecondaryText: string
  ctaSecondaryLink: string
  secondTitle: string
  features: HeroFeature[]
  stats: HeroStats[]
  scrollText: string
}

/**
 * About service item
 */
export interface AboutService {
  id: number
  title: string
  description: string
  icon: string | null
}

/**
 * About stats item
 */
export interface AboutStats {
  id: number
  label: string
  value: string
}

/**
 * About section data from Strapi
 */
export interface AboutData extends StrapiData {
  title: string
  description: RichTextContent[]
  stats: AboutStats[]
  services: AboutService[]
}

/**
 * Project process step item
 */
export interface ProjectStep {
  id: number
  step: string
  title: string
  subTitle: string
}

/**
 * Project process feature item
 */
export interface ProjectFeature {
  id: number
  title: string
  description: string
  icon: string
}

/**
 * Project process section data from Strapi
 */
export interface ProjectProcessData extends StrapiData {
  title: string
  subTitle: string
  Steps: ProjectStep[]
  Features: ProjectFeature[]
}

/**
 * Utility function to extract plain text from rich text content
 */
export function extractTextFromRichText(richText: RichTextContent[]): string {
  return richText
    .map(block =>
      block.children
        .map(child => child.text)
        .join('')
    )
    .join(' ')
}
