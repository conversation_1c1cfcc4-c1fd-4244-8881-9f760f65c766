import { useState, useEffect } from 'react'
import { fetchWithErrorHandling, ApiError } from '../api'
import { AboutData } from '../types'
import { endpoints } from '../config'

interface UseAboutDataReturn {
  data: AboutData | null
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
}

/**
 * Custom hook to fetch about data from Strapi
 */
export function useAboutData(): UseAboutDataReturn {
  const [data, setData] = useState<AboutData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ApiError | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await fetchWithErrorHandling<AboutData>(endpoints.about)
      setData(result.data)
      setError(result.error)
    } catch (err) {
      setError({
        message: err instanceof Error ? err.message : 'Failed to fetch about data',
        details: err,
      })
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchData()
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    data,
    loading,
    error,
    refetch,
  }
}
