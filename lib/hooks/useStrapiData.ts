import { useState, useEffect } from 'react'
import { fetchWithErrorHandling, ApiError } from '../api'

interface UseStrapiDataReturn<T> {
  data: T | null
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
}

/**
 * Generic hook to fetch data from any Strapi endpoint
 * @param endpoint - The API endpoint to fetch from
 * @param dependencies - Array of dependencies that trigger refetch when changed
 */
export function useStrapiData<T = any>(
  endpoint: string,
  dependencies: any[] = []
): UseStrapiDataReturn<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ApiError | null>(null)

  const fetchData = async () => {
    if (!endpoint) {
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await fetchWithErrorHandling<T>(endpoint)
      setData(result.data)
      setError(result.error)
    } catch (err) {
      setError({
        message: err instanceof Error ? err.message : 'Failed to fetch data',
        details: err,
      })
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchData()
  }

  useEffect(() => {
    fetchData()
  }, [endpoint, ...dependencies])

  return {
    data,
    loading,
    error,
    refetch,
  }
}

/**
 * Hook for fetching multiple endpoints at once
 * @param endpoints - Object with endpoint names as keys and endpoint URLs as values
 */
export function useMultipleStrapiData<T extends Record<string, any>>(
  endpoints: Record<keyof T, string>
): {
  data: Partial<T>
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
} {
  const [data, setData] = useState<Partial<T>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ApiError | null>(null)

  const fetchAllData = async () => {
    setLoading(true)
    setError(null)

    try {
      const promises = Object.entries(endpoints).map(async ([key, endpoint]) => {
        const result = await fetchWithErrorHandling(endpoint as string)
        return { key, data: result.data, error: result.error }
      })

      const results = await Promise.all(promises)
      const newData: Partial<T> = {}
      let hasError = false

      results.forEach(({ key, data: resultData, error: resultError }) => {
        if (resultError) {
          hasError = true
          setError(resultError)
        } else {
          newData[key as keyof T] = resultData
        }
      })

      setData(newData)
    } catch (err) {
      setError({
        message: err instanceof Error ? err.message : 'Failed to fetch data',
        details: err,
      })
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchAllData()
  }

  useEffect(() => {
    fetchAllData()
  }, [])

  return {
    data,
    loading,
    error,
    refetch,
  }
}
