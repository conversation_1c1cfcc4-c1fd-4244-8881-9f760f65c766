import { useState, useEffect } from 'react'
import { fetchWithErrorHandling, ApiError } from '../api'
import { ProjectProcessData } from '../types'
import { endpoints } from '../config'

interface UseProjectProcessDataReturn {
  data: ProjectProcessData | null
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
}

/**
 * Custom hook to fetch project process data from Strapi
 */
export function useProjectProcessData(): UseProjectProcessDataReturn {
  const [data, setData] = useState<ProjectProcessData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ApiError | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await fetchWithErrorHandling<ProjectProcessData>(endpoints.projectProcess)
      setData(result.data)
      setError(result.error)
    } catch (err) {
      setError({
        message: err instanceof Error ? err.message : 'Failed to fetch project process data',
        details: err,
      })
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchData()
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    data,
    loading,
    error,
    refetch,
  }
}
