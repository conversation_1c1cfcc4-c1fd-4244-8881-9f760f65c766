import { useState, useEffect } from 'react'
import { fetchWithErrorHandling, ApiError } from '../api'
import { HeroData } from '../types'
import { endpoints } from '../config'

interface UseHeroDataReturn {
  data: HeroData | null
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
}

/**
 * Custom hook to fetch hero data from Strapi
 */
export function useHeroData(): UseHeroDataReturn {
  const [data, setData] = useState<HeroData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ApiError | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await fetchWithErrorHandling<HeroData>(endpoints.hero)
      setData(result.data)
      setError(result.error)
    } catch (err) {
      setError({
        message: err instanceof Error ? err.message : 'Failed to fetch hero data',
        details: err,
      })
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchData()
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    data,
    loading,
    error,
    refetch,
  }
}
