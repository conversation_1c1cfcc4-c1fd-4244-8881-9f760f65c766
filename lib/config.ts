/**
 * Configuration for Strapi API integration
 */

export const config = {
  strapi: {
    url: process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337',
    // API token should only be used server-side for security
    apiToken: typeof window === 'undefined' ? process.env.STRAPI_API_TOKEN || '' : '',
  },
} as const

// Client-side configuration (without sensitive data)
export const clientConfig = {
  strapi: {
    url: process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337',
  },
} as const

export const endpoints = {
  hero: 'hero?populate=*',
  about: 'about?populate=*',
  projectProcess: 'handle-your-project?populate=*',
  services: 'services?populate=*',
  contact: 'contact?populate=*',
  projects: 'projects?populate=*',
  team: 'team?populate=*',
  testimonials: 'testimonials?populate=*',
  // Add more endpoints here as needed
} as const
