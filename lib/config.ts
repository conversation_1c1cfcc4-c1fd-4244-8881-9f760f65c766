/**
 * Configuration for Strapi API integration
 */

export const config = {
  strapi: {
    url: process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337',
    apiToken: process.env.STRAPI_API_TOKEN || '',
  },
} as const

export const endpoints = {
  hero: '/api/hero?populate=*',
  about: '/api/about?populate=*',
  services: '/api/services?populate=*',
  contact: '/api/contact?populate=*',
  projects: '/api/projects?populate=*',
  team: '/api/team?populate=*',
  testimonials: '/api/testimonials?populate=*',
  // Add more endpoints here as needed
} as const
