"use client"

import { useState, useEffect } from "react"
import { CheckCir<PERSON>, Clock, Target } from "lucide-react"

const processSteps = [
  {
    id: 1,
    title: "Project Start",
    description: "Initial consultation and project kickoff",
  },
  {
    id: 2,
    title: "Requirements",
    description: "Understanding your needs and objectives",
  },
  {
    id: 3,
    title: "Planning",
    description: "Architecture planning and design",
  },
  {
    id: 4,
    title: "Development",
    description: "Coding and implementation",
  },
  {
    id: 5,
    title: "Testing",
    description: "Quality assurance and optimization",
  },
  {
    id: 6,
    title: "Delivery",
    description: "Launch and final handover",
  },
]

export function ProjectProcess() {
  const [activeStep, setActiveStep] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % processSteps.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-20 px-4 bg-gray-950">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-white mb-4">How We Handle Your Project</h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Our transparent, step-by-step approach ensures quality delivery every time.
          </p>
        </div>

        <div className="relative mb-16">
          {/* Simple progress line */}
          <div className="absolute top-8 left-0 right-0 h-0.5 bg-gray-800">
            <div
              className="h-full bg-[#17CED9] transition-all duration-1000 ease-out"
              style={{ width: `${((activeStep + 1) / processSteps.length) * 100}%` }}
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {processSteps.map((step, index) => (
              <div key={step.id} className="text-center">
                <div
                  className={`w-16 h-16 rounded-full border-2 flex items-center justify-center text-sm font-bold mb-4 mx-auto transition-all duration-500 ${
                    index <= activeStep
                      ? "bg-[#17CED9] border-[#17CED9] text-gray-900"
                      : "bg-gray-800 border-gray-600 text-gray-400"
                  }`}
                >
                  {step.id}
                </div>

                <h3
                  className={`font-semibold text-sm mb-2 transition-colors duration-300 ${
                    index <= activeStep ? "text-white" : "text-gray-500"
                  }`}
                >
                  {step.title}
                </h3>
                <p className="text-xs text-gray-600 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="text-center p-6 bg-gray-900/30 rounded-lg">
            <CheckCircle className="w-8 h-8 text-[#17CED9] mx-auto mb-3" />
            <h4 className="font-semibold text-white mb-2">Transparent</h4>
            <p className="text-gray-400 text-sm">Clear communication at every step</p>
          </div>

          <div className="text-center p-6 bg-gray-900/30 rounded-lg">
            <Target className="w-8 h-8 text-[#17CED9] mx-auto mb-3" />
            <h4 className="font-semibold text-white mb-2">Quality Focused</h4>
            <p className="text-gray-400 text-sm">Rigorous testing and optimization</p>
          </div>

          <div className="text-center p-6 bg-gray-900/30 rounded-lg">
            <Clock className="w-8 h-8 text-[#17CED9] mx-auto mb-3" />
            <h4 className="font-semibold text-white mb-2">On Schedule</h4>
            <p className="text-gray-400 text-sm">Delivered on time, every time</p>
          </div>
        </div>
      </div>
    </section>
  )
}
