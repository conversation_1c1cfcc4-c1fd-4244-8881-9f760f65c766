"use client"

import { useState, useEffect } from "react"
import { CheckCircle, Clock, Target, Users, Shield, Zap } from "lucide-react"
import { useProjectProcessData } from "@/lib/hooks/useProjectProcessData"
import "@/styles/project-process.css"

export function ProjectProcess() {
  const [activeStep, setActiveStep] = useState(0)
  const { data: projectData, loading, error } = useProjectProcessData()

  // Extract data with fallbacks (moved before conditional returns)
  const title = projectData?.title || "How We Handle Your Project"
  const subTitle = projectData?.subTitle || "Our transparent, step-by-step approach ensures quality delivery every time."

  const processSteps = projectData?.Steps || [
    {
      id: 1,
      step: "1",
      title: "Project Start",
      subTitle: "Initial consultation and project kickoff",
    },
    {
      id: 2,
      step: "2",
      title: "Requirements",
      subTitle: "Understanding your needs and objectives",
    },
    {
      id: 3,
      step: "3",
      title: "Planning",
      subTitle: "Architecture planning and design",
    },
    {
      id: 4,
      step: "4",
      title: "Development",
      subTitle: "Coding and implementation",
    },
    {
      id: 5,
      step: "5",
      title: "Testing",
      subTitle: "Quality assurance and optimization",
    },
    {
      id: 6,
      step: "6",
      title: "Delivery",
      subTitle: "Launch and final handover",
    },
  ]

  const features = projectData?.Features || [
    {
      id: 1,
      title: "Transparent",
      description: "Clear communication at every step",
      icon: "CheckCircle"
    },
    {
      id: 2,
      title: "Quality Focused",
      description: "Rigorous testing and optimization",
      icon: "Target"
    },
    {
      id: 3,
      title: "On Schedule",
      description: "Delivered on time, every time",
      icon: "Clock"
    }
  ]

  // useEffect must be called before any conditional returns
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % processSteps.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [processSteps.length])

  // Show loading state
  if (loading) {
    return (
      <section className="py-20 px-4 bg-gray-950">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-10 bg-gray-700 rounded w-1/2 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-700 rounded w-2/3 mx-auto"></div>
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-16">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="text-center animate-pulse">
                <div className="w-16 h-16 bg-gray-700 rounded-full mx-auto mb-4"></div>
                <div className="h-4 bg-gray-700 rounded mb-2"></div>
                <div className="h-3 bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Show error state
  if (error) {
    return (
      <section className="py-20 px-4 bg-gray-950">
        <div className="max-w-6xl mx-auto text-center">
          <div className="text-red-500 mb-4">Error loading project process data: {error.message}</div>
          <div className="text-gray-400">Using fallback content...</div>
        </div>
      </section>
    )
  }



  return (
    <section className="py-20 px-4 bg-gray-950">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-white mb-4">{title}</h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            {subTitle}
          </p>
        </div>

        <div className="relative mb-16">
          {/* Simple progress line */}
          <div className="absolute top-8 left-0 right-0 h-0.5 bg-gray-800">
            <div
              className="h-full bg-[#17CED9] transition-all duration-1000 ease-out"
              style={{ width: `${((activeStep + 1) / processSteps.length) * 100}%` }}
            />
          </div>

          <div
            className="dynamic-process-grid"
            style={{
              '--steps-count': processSteps.length
            } as React.CSSProperties}
          >
            {processSteps.map((step, index) => (
              <div key={step.id} className="text-center">
                <div
                  className={`w-16 h-16 rounded-full border-2 flex items-center justify-center text-sm font-bold mb-4 mx-auto transition-all duration-500 ${
                    index <= activeStep
                      ? "bg-[#17CED9] border-[#17CED9] text-gray-900"
                      : "bg-gray-800 border-gray-600 text-gray-400"
                  }`}
                >
                  {step.step}
                </div>

                <h3
                  className={`font-semibold text-sm mb-2 transition-colors duration-300 ${
                    index <= activeStep ? "text-white" : "text-gray-500"
                  }`}
                >
                  {step.title}
                </h3>
                <p className="text-xs text-gray-600 leading-relaxed">{step.subTitle}</p>
              </div>
            ))}
          </div>
        </div>

        <div className={`grid gap-6 max-w-4xl mx-auto ${features.length <= 2 ? 'grid-cols-2' : features.length === 3 ? 'md:grid-cols-3' : 'md:grid-cols-4'}`}>
          {features.map((feature) => {
            // Icon mapping for features
            const iconMap: Record<string, any> = {
              'CheckCircle': CheckCircle,
              'Target': Target,
              'Clock': Clock,
              'Users': Users,
              'Shield': Shield,
              'Zap': Zap,
            }

            // Get icon component or fallback to CheckCircle
            const IconComponent = feature.icon && iconMap[feature.icon]
              ? iconMap[feature.icon]
              : CheckCircle

            return (
              <div key={feature.id} className="text-center p-6 bg-gray-900/30 rounded-lg">
                <IconComponent className="w-8 h-8 text-[#17CED9] mx-auto mb-3" />
                <h4 className="font-semibold text-white mb-2">{feature.title}</h4>
                <p className="text-gray-400 text-sm">{feature.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
