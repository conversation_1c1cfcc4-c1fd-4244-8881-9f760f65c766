"use client"

import { useState } from 'react'
import { fetchFromStrapi } from '@/lib/api'
import { Button } from '@/components/ui/button'

export function ApiTest() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testApi = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetchFromStrapi('/api/hero?populate=*')
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 border rounded-lg max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">API Test Component</h2>
      
      <Button onClick={testApi} disabled={loading}>
        {loading ? 'Testing...' : 'Test Hero API'}
      </Button>

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <strong>Success!</strong>
          <pre className="mt-2 text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
