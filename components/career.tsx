"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function Career() {
  const [hoveredJob, setHoveredJob] = useState<number | null>(null)

  const jobOpenings = [
    {
      title: "Senior AI Engineer",
      department: "AI & Machine Learning",
      location: "Remote / Hybrid",
      type: "Full-time",
      experience: "5+ years",
      description: "Lead AI model development and implementation for cutting-edge projects.",
    },
    {
      title: "Full Stack Developer",
      department: "Software Development",
      location: "On-site",
      type: "Full-time",
      experience: "3+ years",
      description: "Build scalable web applications using modern tech stack.",
    },
    {
      title: "Electronics Engineer",
      department: "Hardware Engineering",
      location: "On-site",
      type: "Full-time",
      experience: "4+ years",
      description: "Design and develop electronic systems and embedded solutions.",
    },
    {
      title: "DevOps Engineer",
      department: "Infrastructure",
      location: "Remote",
      type: "Full-time",
      experience: "3+ years",
      description: "Manage cloud infrastructure and deployment pipelines.",
    },
  ]

  const benefits = [
    {
      icon: "🚀",
      title: "Innovation First",
      description: "Work on cutting-edge AI and tech projects that shape the future",
    },
    {
      icon: "💡",
      title: "Learning Culture",
      description: "Continuous learning opportunities and skill development programs",
    },
    {
      icon: "🏆",
      title: "Competitive Package",
      description: "Attractive salary, equity options, and comprehensive benefits",
    },
    {
      icon: "🌍",
      title: "Flexible Work",
      description: "Remote-first culture with flexible working arrangements",
    },
    {
      icon: "👥",
      title: "Expert Team",
      description: "Collaborate with industry experts and thought leaders",
    },
    {
      icon: "⚡",
      title: "Fast Growth",
      description: "Rapid career progression in a high-growth environment",
    },
  ]

  return (
    <section className="py-20 px-4 bg-gray-950 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 border border-[#17CED] rounded-full"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 border border-[#17CED] rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-[#17CED] rounded-full blur-xl"></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-6">
            Join Our <span className="text-[#17CED]">Team</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Be part of a revolutionary team that's shaping the future of AI and technology. Discover exciting career
            opportunities at TechLabs.
          </p>
        </div>

        {/* Why Work With Us */}
        <div className="mb-20">
          <h3 className="text-3xl font-serif font-bold text-white text-center mb-12">
            Why Work With <span className="text-[#17CED]">TechLabs</span>?
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card
                key={index}
                className="bg-gray-900/50 border-gray-800 hover:border-[#17CED]/50 transition-all duration-300 hover:transform hover:scale-105"
              >
                <CardHeader className="text-center pb-4">
                  <div className="text-4xl mb-4">{benefit.icon}</div>
                  <CardTitle className="text-white text-xl font-serif">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 text-center leading-relaxed">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Job Openings */}
        <div>
          <h3 className="text-3xl font-serif font-bold text-white text-center mb-12">
            Current <span className="text-[#17CED]">Opportunities</span>
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {jobOpenings.map((job, index) => (
              <Card
                key={index}
                className="bg-gray-900/50 border-gray-800 hover:border-[#17CED]/50 transition-all duration-300 cursor-pointer"
                onMouseEnter={() => setHoveredJob(index)}
                onMouseLeave={() => setHoveredJob(null)}
              >
                <CardHeader>
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <CardTitle className="text-white text-xl font-serif mb-2">{job.title}</CardTitle>
                      <CardDescription className="text-[#17CED] font-medium">{job.department}</CardDescription>
                    </div>
                    <Badge variant="outline" className="border-[#17CED] text-[#17CED] bg-[#17CED]/10">
                      {job.type}
                    </Badge>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge variant="secondary" className="bg-gray-800 text-gray-300">
                      📍 {job.location}
                    </Badge>
                    <Badge variant="secondary" className="bg-gray-800 text-gray-300">
                      ⏱️ {job.experience}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-300 mb-6 leading-relaxed">{job.description}</p>

                  <Button
                    className={`w-full transition-all duration-300 ${
                      hoveredJob === index
                        ? "bg-[#17CED] hover:bg-[#17CED]/90 text-black"
                        : "bg-transparent border border-[#17CED] text-[#17CED] hover:bg-[#17CED] hover:text-black"
                    }`}
                  >
                    Apply Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-gray-300 text-lg mb-8">
            Don't see a perfect match? We're always looking for talented individuals.
          </p>
          <Button size="lg" className="bg-[#17CED] hover:bg-[#17CED]/90 text-black font-medium px-8 py-3">
            Send Us Your Resume
          </Button>
        </div>
      </div>
    </section>
  )
}
