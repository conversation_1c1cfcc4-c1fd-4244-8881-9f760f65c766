"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Brain, Code, Zap, TrendingUp, ChevronDown } from "lucide-react"
import { useEffect, useState } from "react"
import { useHeroData } from "@/lib/hooks/useHeroData"
import { extractTextFromRichText } from "@/lib/types"

const ParticleBackground = () => {
  const [particles, setParticles] = useState<
    Array<{
      id: number
      x: number
      y: number
      size: number
      opacity: number
      speedX: number
      speedY: number
    }>
  >([])

  useEffect(() => {
    // Generate particles
    const newParticles = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      opacity: Math.random() * 0.5 + 0.1,
      speedX: (Math.random() - 0.5) * 0.5,
      speedY: (Math.random() - 0.5) * 0.5,
    }))
    setParticles(newParticles)

    // Animate particles
    const interval = setInterval(() => {
      setParticles((prev) =>
        prev.map((particle) => ({
          ...particle,
          x: (particle.x + particle.speedX + 100) % 100,
          y: (particle.y + particle.speedY + 100) % 100,
        })),
      )
    }, 100)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 bg-primary rounded-full transition-all duration-1000 ease-linear"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 2}px rgba(23, 206, 221, 0.3)`,
          }}
        />
      ))}

      {/* Connection lines between nearby particles */}
      <svg className="absolute inset-0 w-full h-full">
        {particles.map((particle, i) =>
          particles.slice(i + 1).map((otherParticle, j) => {
            const distance = Math.sqrt(
              Math.pow(particle.x - otherParticle.x, 2) + Math.pow(particle.y - otherParticle.y, 2),
            )
            if (distance < 15) {
              return (
                <line
                  key={`${i}-${j}`}
                  x1={`${particle.x}%`}
                  y1={`${particle.y}%`}
                  x2={`${otherParticle.x}%`}
                  y2={`${otherParticle.y}%`}
                  stroke="rgba(23, 206, 221, 0.2)"
                  strokeWidth="0.5"
                  className="animate-pulse"
                />
              )
            }
            return null
          }),
        )}
      </svg>
    </div>
  )
}

export function Hero() {
  const [isVisible, setIsVisible] = useState(false)
  const [counters, setCounters] = useState({ satisfaction: 0, roi: 0, monitoring: 0 })
  const { data: heroData, loading, error } = useHeroData()

  useEffect(() => {
    setIsVisible(true)

    // Animate counters
    const animateCounter = (target: number, key: keyof typeof counters, suffix = "") => {
      let current = 0
      const increment = target / 50
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          current = target
          clearInterval(timer)
        }
        setCounters((prev) => ({ ...prev, [key]: Math.floor(current) }))
      }, 30)
    }

    setTimeout(() => {
      animateCounter(98, "satisfaction")
      animateCounter(25, "roi") // 2.5x = 25 (we'll divide by 10 in display)
      animateCounter(24, "monitoring")
    }, 1000)
  }, [])

  // Show loading state
  if (loading) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-card to-background" />
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-primary/20 rounded-full w-48 mx-auto mb-8"></div>
            <div className="h-16 bg-primary/20 rounded w-3/4 mx-auto mb-6"></div>
            <div className="h-6 bg-primary/20 rounded w-2/3 mx-auto mb-8"></div>
          </div>
        </div>
      </section>
    )
  }

  // Show error state
  if (error) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-card to-background" />
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="text-red-500 mb-4">Error loading hero data: {error.message}</div>
          <div className="text-muted-foreground">Using fallback content...</div>
        </div>
      </section>
    )
  }

  // Extract data with fallbacks
  const title = heroData?.title || "Transform Your Business with"
  const secondTitle = heroData?.secondTitle || "Next-Gen AI"
  const subtitle = heroData?.subtitle ? extractTextFromRichText(heroData.subtitle) :
    "We don't just build software—we architect intelligent ecosystems that learn, adapt, and scale with your ambitions. Join the AI revolution today."
  const ctaPrimaryText = heroData?.ctaPrimaryText || "Get Free AI Consultation"
  const ctaPrimaryLink = heroData?.ctaPrimaryLink || "#"
  const ctaSecondaryText = heroData?.ctaSecondaryText || "View Case Studies"
  const ctaSecondaryLink = heroData?.ctaSecondaryLink || "#"
  const features = heroData?.features || [
    {
      id: 1,
      title: "AI-First Architecture",
      description: "Machine learning models that evolve with your data, delivering insights that drive real business outcomes"
    },
    {
      id: 2,
      title: "Enterprise-Grade Solutions",
      description: "Scalable, secure, and maintainable software built with cutting-edge technologies and industry best practices"
    },
    {
      id: 3,
      title: "Measurable Impact",
      description: "Track ROI with real-time analytics and performance metrics that prove the value of your AI investment"
    }
  ]

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-background via-card to-background" />

      <ParticleBackground />

      {/* Enhanced animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-primary/5 rounded-full blur-3xl animate-pulse delay-500" />

        <div className="absolute top-20 left-10 w-2 h-2 bg-primary/30 rounded-full animate-ping delay-700" />
        <div className="absolute top-40 right-20 w-1 h-1 bg-primary/40 rounded-full animate-ping delay-1200" />
        <div className="absolute bottom-32 left-32 w-3 h-3 bg-primary/20 rounded-full animate-ping delay-300" />
        <div className="absolute bottom-20 right-40 w-2 h-2 bg-primary/35 rounded-full animate-ping delay-900" />
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div
          className={`inline-flex items-center space-x-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-8 backdrop-blur-sm transition-all duration-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"}`}
        >
          <Zap className="w-4 h-4 text-primary animate-pulse" />
          <span className="text-sm font-medium text-primary">Trusted by 500+ Companies</span>
        </div>

        <h1
          className={`font-serif font-bold text-4xl sm:text-6xl lg:text-8xl text-foreground mb-6 leading-tight transition-all duration-1000 delay-200 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
        >
          {title}{" "}
          <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent animate-pulse">
            {secondTitle}
          </span>
        </h1>

        <p
          className={`text-xl sm:text-2xl text-muted-foreground max-w-4xl mx-auto mb-8 leading-relaxed font-light transition-all duration-1000 delay-400 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-6"}`}
        >
          {subtitle}
        </p>

        <div
          className={`flex flex-wrap items-center justify-center gap-8 mb-10 text-sm transition-all duration-1000 delay-600 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"}`}
        >
          <div className="flex items-center space-x-2 text-muted-foreground group hover:scale-105 transition-transform duration-300">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span>
              <span className="text-primary font-semibold text-lg">{counters.satisfaction}%</span> Client Satisfaction
            </span>
          </div>
          <div className="flex items-center space-x-2 text-muted-foreground group hover:scale-105 transition-transform duration-300">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse delay-200"></div>
            <span>
              <span className="text-primary font-semibold text-lg">{(counters.roi / 10).toFixed(1)}x</span> Average ROI
            </span>
          </div>
          <div className="flex items-center space-x-2 text-muted-foreground group hover:scale-105 transition-transform duration-300">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse delay-400"></div>
            <span>
              <span className="text-primary font-semibold text-lg">{counters.monitoring}/7</span> AI Monitoring
            </span>
          </div>
        </div>

        <div
          className={`flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 transition-all duration-1000 delay-800 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"}`}
        >
          <Button
            size="lg"
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-primary/25 transition-all duration-300 hover:scale-105 hover:-translate-y-1"
            onClick={() => window.open(ctaPrimaryLink, '_self')}
          >
            {ctaPrimaryText}
            <ArrowRight className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="px-10 py-4 text-lg bg-transparent border-primary/30 hover:bg-primary/5 hover:border-primary/50 transition-all duration-300 hover:scale-105"
            onClick={() => window.open(ctaSecondaryLink, '_self')}
          >
            {ctaSecondaryText}
          </Button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-5xl mx-auto mb-16">
          {features.map((feature, index) => {
            const icons = [Brain, Code, TrendingUp]
            const IconComponent = icons[index % icons.length]

            return (
              <div
                key={feature.id}
                className={`group flex flex-col items-center space-y-4 p-6 rounded-xl bg-card/50 backdrop-blur-sm border border-primary/10 hover:border-primary/30 transition-all duration-500 hover:scale-105 hover:-translate-y-2 hover:shadow-xl hover:shadow-primary/10 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
                style={{ transitionDelay: `${1000 + index * 200}ms` }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                  <IconComponent className="w-8 h-8 text-primary group-hover:animate-pulse" />
                </div>
                <h3 className="font-serif font-bold text-xl text-foreground">{feature.title}</h3>
                <p className="text-muted-foreground text-center leading-relaxed">
                  {feature.description}
                </p>
              </div>
            )
          })}
        </div>

        <div
          className={`flex flex-col items-center space-y-4 transition-all duration-1000 delay-1600 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"}`}
        >
          <p className="text-sm text-muted-foreground">Discover how we've transformed 500+ businesses</p>
          <div className="animate-bounce hover:animate-pulse cursor-pointer group">
            <ChevronDown className="w-6 h-6 text-primary group-hover:scale-110 transition-transform duration-300" />
          </div>
        </div>
      </div>
    </section>
  )
}
