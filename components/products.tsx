"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink, Users, Calendar, Code, Zap, Brain, Shield } from "lucide-react"

const products = [
  {
    id: 1,
    name: "AI-Powered Analytics Dashboard",
    description:
      "Advanced business intelligence platform that leverages machine learning to provide real-time insights and predictive analytics for enterprise clients.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["React", "Python", "TensorFlow", "AWS"],
    team: ["<PERSON>", "<PERSON>", "<PERSON>"],
    duration: "8 months",
    category: "AI/ML",
    status: "Live",
    features: ["Real-time data processing", "Predictive modeling", "Custom dashboards"],
    icon: <Brain className="w-6 h-6" />,
  },
  {
    id: 2,
    name: "Smart IoT Management System",
    description:
      "Comprehensive IoT device management platform with electronic engineering integration for industrial automation and monitoring.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Node.js", "MongoDB", "Arduino", "MQTT"],
    team: ["<PERSON>", "<PERSON>", "Tom Anderson"],
    duration: "6 months",
    category: "IoT/Electronics",
    status: "Live",
    features: ["Device monitoring", "Remote control", "Data visualization"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: 3,
    name: "Secure Cloud Infrastructure",
    description:
      "Enterprise-grade cloud security solution with automated threat detection and response capabilities for financial institutions.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["Kubernetes", "Docker", "Go", "PostgreSQL"],
    team: ["Emma Thompson", "James Wilson", "Raj Patel"],
    duration: "12 months",
    category: "Security",
    status: "In Development",
    features: ["Threat detection", "Automated response", "Compliance monitoring"],
    icon: <Shield className="w-6 h-6" />,
  },
  {
    id: 4,
    name: "AI Code Assistant",
    description:
      "Intelligent code generation and review tool that helps developers write better code faster using advanced language models.",
    image: "/placeholder.svg?height=300&width=400",
    technologies: ["TypeScript", "OpenAI API", "VS Code Extension", "Redis"],
    team: ["Kevin Lee", "Anna Martinez", "Chris Brown"],
    duration: "4 months",
    category: "Developer Tools",
    status: "Beta",
    features: ["Code generation", "Bug detection", "Performance optimization"],
    icon: <Code className="w-6 h-6" />,
  },
]

export function Products() {
  const [selectedCategory, setSelectedCategory] = useState("All")

  const categories = ["All", "AI/ML", "IoT/Electronics", "Security", "Developer Tools"]

  const filteredProducts =
    selectedCategory === "All" ? products : products.filter((product) => product.category === selectedCategory)

  return (
    <section className="py-20 px-4 bg-gray-950">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-6">Our Latest Products</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Discover our cutting-edge solutions that combine AI innovation, electronic engineering expertise, and
            software development excellence to deliver transformative results.
          </p>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category
                    ? "bg-[#17CED] hover:bg-[#17CED]/90 text-black"
                    : "border-gray-600 text-gray-300 hover:bg-gray-800"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {filteredProducts.map((product, index) => (
            <Card
              key={product.id}
              className="bg-gray-900 border-gray-800 hover:border-[#17CED]/50 transition-all duration-300 group hover:shadow-2xl hover:shadow-[#17CED]/10"
              style={{
                animationDelay: `${index * 0.1}s`,
              }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={product.image || "/placeholder.svg"}
                  alt={product.name}
                  className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute top-4 left-4">
                  <Badge
                    variant="secondary"
                    className={`${
                      product.status === "Live"
                        ? "bg-green-500/20 text-green-400 border-green-500/30"
                        : product.status === "Beta"
                          ? "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
                          : "bg-blue-500/20 text-blue-400 border-blue-500/30"
                    }`}
                  >
                    {product.status}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4 bg-[#17CED]/20 p-2 rounded-full">{product.icon}</div>
              </div>

              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-white text-xl mb-2 group-hover:text-[#17CED] transition-colors">
                      {product.name}
                    </CardTitle>
                    <Badge variant="outline" className="border-[#17CED]/30 text-[#17CED] mb-3">
                      {product.category}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="text-gray-300 leading-relaxed">{product.description}</CardDescription>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Key Features */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-400 mb-2">Key Features</h4>
                  <div className="flex flex-wrap gap-2">
                    {product.features.map((feature, idx) => (
                      <Badge key={idx} variant="secondary" className="bg-gray-800 text-gray-300 text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Technologies */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-400 mb-2">Technologies</h4>
                  <div className="flex flex-wrap gap-2">
                    {product.technologies.map((tech, idx) => (
                      <Badge key={idx} variant="outline" className="border-gray-600 text-gray-300 text-xs">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Team & Duration */}
                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div>
                    <div className="flex items-center gap-2 text-gray-400 mb-2">
                      <Users className="w-4 h-4" />
                      <span className="text-sm font-semibold">Team</span>
                    </div>
                    <div className="space-y-1">
                      {product.team.map((member, idx) => (
                        <p key={idx} className="text-sm text-gray-300">
                          {member}
                        </p>
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 text-gray-400 mb-2">
                      <Calendar className="w-4 h-4" />
                      <span className="text-sm font-semibold">Duration</span>
                    </div>
                    <p className="text-sm text-gray-300">{product.duration}</p>
                  </div>
                </div>

                {/* Action Button */}
                <div className="pt-4">
                  <Button className="w-full bg-[#17CED] hover:bg-[#17CED]/90 text-black font-semibold group">
                    View Details
                    <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-gray-300 mb-6">
            Interested in learning more about our products or discussing a custom solution?
          </p>
          <Button size="lg" className="bg-[#17CED] hover:bg-[#17CED]/90 text-black font-semibold px-8">
            Get In Touch
          </Button>
        </div>
      </div>
    </section>
  )
}
