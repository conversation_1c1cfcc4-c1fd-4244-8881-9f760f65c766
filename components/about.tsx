export function About() {
  return (
    <section className="py-24 bg-gray-900/50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#17CED]/5 to-transparent"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-6">
              About <span className="text-[#17CED]">TechLabs</span>
            </h2>
            <div className="w-24 h-1 bg-[#17CED] mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              Founded with a vision to revolutionize the tech landscape, TechLabs has been at the forefront of
              innovation since our inception. We combine cutting-edge technology with deep industry expertise to deliver
              solutions that transform businesses and drive the future forward.
            </p>
          </div>

          {/* Expertise cards */}
          <div className="grid md:grid-cols-3 gap-8">
            {/* Expert Software Development */}
            <div className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#17CED]/50 transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-16 h-16 bg-[#17CED]/20 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#17CED]/30 transition-colors">
                <svg className="w-8 h-8 text-[#17CED]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-serif font-bold text-white mb-4">Expert Software Development</h3>
              <p className="text-gray-300 leading-relaxed">
                Our seasoned development team crafts robust, scalable solutions using the latest technologies and best
                practices. From web applications to enterprise systems, we deliver excellence.
              </p>
            </div>

            {/* Electronic Engineering */}
            <div className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#17CED]/50 transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-16 h-16 bg-[#17CED]/20 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#17CED]/30 transition-colors">
                <svg className="w-8 h-8 text-[#17CED]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-serif font-bold text-white mb-4">Electronic Engineering</h3>
              <p className="text-gray-300 leading-relaxed">
                Deep expertise in electronic systems design, embedded solutions, and hardware-software integration. We
                bridge the gap between digital innovation and physical implementation.
              </p>
            </div>

            {/* Gen AI Expertise */}
            <div className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#17CED]/50 transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-16 h-16 bg-[#17CED]/20 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#17CED]/30 transition-colors">
                <svg className="w-8 h-8 text-[#17CED]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-serif font-bold text-white mb-4">Gen AI Expertise</h3>
              <p className="text-gray-300 leading-relaxed">
                Leading the AI revolution with advanced generative AI solutions. From machine learning models to
                intelligent automation, we harness AI's power to solve complex challenges.
              </p>
            </div>
          </div>

          {/* Company stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[#17CED] mb-2">5+</div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[#17CED] mb-2">100+</div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[#17CED] mb-2">50+</div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[#17CED] mb-2">24/7</div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">Support</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
