"use client"

import { useAboutData } from "@/lib/hooks/useAboutData"
import { extractTextFromRichText } from "@/lib/types"
import { Code, Cpu, Lightbulb } from "lucide-react"

export function About() {
  const { data: aboutData, loading, error } = useAboutData()

  // Show loading state
  if (loading) {
    return (
      <section className="py-24 bg-gray-900/50 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#17CED]/5 to-transparent"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-12 bg-gray-700 rounded w-1/2 mx-auto mb-8"></div>
              <div className="h-6 bg-gray-700 rounded w-3/4 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-700 rounded w-2/3 mx-auto mb-16"></div>
              <div className="grid md:grid-cols-3 gap-8">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-64 bg-gray-700 rounded-2xl"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Show error state
  if (error) {
    return (
      <section className="py-24 bg-gray-900/50 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#17CED]/5 to-transparent"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-red-500 mb-4">Error loading about data: {error.message}</div>
            <div className="text-gray-400">Using fallback content...</div>
          </div>
        </div>
      </section>
    )
  }

  // Extract data with fallbacks
  const title = aboutData?.title || "About TechLabs"
  const description = aboutData?.description ? extractTextFromRichText(aboutData.description) :
    "Founded with a vision to revolutionize the tech landscape, TechLabs has been at the forefront of innovation since our inception. We combine cutting-edge technology with deep industry expertise to deliver solutions that transform businesses and drive the future forward."

  const services = aboutData?.services || [
    {
      id: 1,
      title: "Expert Software Development",
      description: "Our seasoned development team crafts robust, scalable solutions using the latest technologies and best practices. From web applications to enterprise systems, we deliver excellence.",
      icon: null
    },
    {
      id: 2,
      title: "Electronic Engineering",
      description: "Deep expertise in electronic systems design, embedded solutions, and hardware-software integration. We bridge the gap between digital innovation and physical implementation.",
      icon: null
    },
    {
      id: 3,
      title: "Gen AI Expertise",
      description: "Leading the AI revolution with advanced generative AI solutions. From machine learning models to intelligent automation, we harness AI's power to solve complex challenges.",
      icon: null
    }
  ]

  const stats = aboutData?.stats || [
    {
      id: 1,
      label: "YEARS EXPERIENCE",
      value: "5+"
    },
    {
      id: 2,
      label: "PROJECTS DELIVERED",
      value: "100+"
    },
    {
      id: 3,
      label: "HAPPY CLIENTS",
      value: "50+"
    },
    {
      id: 4,
      label: "SUPPORT",
      value: "24/7"
    }
  ]
  return (
    <section className="py-24 bg-gray-900/50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#17CED]/5 to-transparent"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-6">
              {title}
            </h2>
            <div className="w-24 h-1 bg-[#17CED] mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              {description}
            </p>
          </div>

          {/* Expertise cards */}
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => {
              // Icon mapping for services
              const iconMap: Record<string, any> = {
                'Code': Code,
                'Cpu': Cpu,
                'Lightbulb': Lightbulb,
              }

              // Get icon component or fallback to default icons
              const IconComponent = service.icon && iconMap[service.icon]
                ? iconMap[service.icon]
                : [Code, Cpu, Lightbulb][index % 3]

              return (
                <div
                  key={service.id}
                  className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#17CED]/50 transition-all duration-300 hover:transform hover:scale-105"
                >
                  <div className="w-16 h-16 bg-[#17CED]/20 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#17CED]/30 transition-colors">
                    <IconComponent className="w-8 h-8 text-[#17CED]" />
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-white mb-4">{service.title}</h3>
                  <p className="text-gray-300 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              )
            })}
          </div>

          {/* Company stats */}
          <div className={`mt-16 grid gap-8 ${stats.length <= 2 ? 'grid-cols-2' : stats.length === 3 ? 'grid-cols-3' : 'grid-cols-2 md:grid-cols-4'}`}>
            {stats.map((stat) => (
              <div key={stat.id} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-[#17CED] mb-2">{stat.value}</div>
                <div className="text-gray-400 text-sm uppercase tracking-wider">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
