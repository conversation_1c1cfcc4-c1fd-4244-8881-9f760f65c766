"use client"

import { useStrapiData } from '@/lib/hooks/useStrapiData'
import { StrapiData } from '@/lib/api'

// Example type for About section data
interface AboutData extends StrapiData {
  title: string
  description: string
  image?: {
    url: string
    alternativeText: string
  }
  features?: Array<{
    id: number
    title: string
    description: string
  }>
}

export function ExampleAboutWithApi() {
  const { data, loading, error, refetch } = useStrapiData<AboutData>('/api/about?populate=*')

  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-8"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <div className="text-red-500 mb-4">
            Error loading about data: {error.message}
          </div>
          <button 
            onClick={refetch}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </section>
    )
  }

  if (!data) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <p>No about data available</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{data.title}</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {data.description}
          </p>
        </div>

        {data.image && (
          <div className="mb-12">
            <img 
              src={`${process.env.NEXT_PUBLIC_STRAPI_URL}${data.image.url}`}
              alt={data.image.alternativeText || data.title}
              className="w-full max-w-4xl mx-auto rounded-lg shadow-lg"
            />
          </div>
        )}

        {data.features && data.features.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {data.features.map((feature) => (
              <div key={feature.id} className="p-6 border rounded-lg">
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}

// Example usage in a page:
/*
import { ExampleAboutWithApi } from '@/components/example-about-with-api'

export default function AboutPage() {
  return (
    <div>
      <ExampleAboutWithApi />
    </div>
  )
}
*/
