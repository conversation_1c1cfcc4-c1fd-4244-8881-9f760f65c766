import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

const geist = Geist({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist",
})

export const metadata: Metadata = {
  title: "TechLabs - AI-Powered IT Solutions",
  description: "Revolutionizing IT with cutting-edge AI technology and software solutions",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${geist.variable} dark`}>
      <body className="font-sans antialiased">{children}</body>
    </html>
  )
}
