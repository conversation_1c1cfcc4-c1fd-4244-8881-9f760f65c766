import { NextRequest, NextResponse } from 'next/server'

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN || ''

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Reconstruct the Strapi API path
    const strapiPath = params.path.join('/')
    const url = new URL(request.url)
    const searchParams = url.searchParams.toString()
    const strapiUrl = `${STRAPI_URL}/api/${strapiPath}${searchParams ? `?${searchParams}` : ''}`

    console.log('Fetching from Strapi:', strapiUrl)

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    // Add authorization header if token is available
    if (STRAPI_API_TOKEN) {
      headers.Authorization = `Bearer ${STRAPI_API_TOKEN}`
    }

    const response = await fetch(strapiUrl, {
      method: 'GET',
      headers,
      next: {
        revalidate: 60, // Cache for 1 minute
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Strapi API Error:', response.status, errorText)
      return NextResponse.json(
        { 
          error: 'Failed to fetch from Strapi', 
          status: response.status,
          message: errorText 
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error) {
    console.error('API Route Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const strapiPath = params.path.join('/')
    const strapiUrl = `${STRAPI_URL}/api/${strapiPath}`
    const body = await request.json()

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    if (STRAPI_API_TOKEN) {
      headers.Authorization = `Bearer ${STRAPI_API_TOKEN}`
    }

    const response = await fetch(strapiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Strapi API Error:', response.status, errorText)
      return NextResponse.json(
        { 
          error: 'Failed to post to Strapi', 
          status: response.status,
          message: errorText 
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error) {
    console.error('API Route Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
