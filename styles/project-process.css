/* Dynamic Process Steps Grid */
.dynamic-process-grid {
  display: grid;
  gap: 2rem;
}

/* Default: All steps in one row on large screens */
.dynamic-process-grid {
  grid-template-columns: repeat(var(--steps-count), minmax(0, 1fr));
}

/* Medium screens: Max 4 columns */
@media (max-width: 1023px) {
  .dynamic-process-grid {
    grid-template-columns: repeat(min(4, var(--steps-count)), minmax(0, 1fr));
  }
}

/* Small screens: Max 2 columns */
@media (max-width: 767px) {
  .dynamic-process-grid {
    grid-template-columns: repeat(min(2, var(--steps-count)), minmax(0, 1fr));
  }
}

/* Very small screens: 1 column */
@media (max-width: 479px) {
  .dynamic-process-grid {
    grid-template-columns: 1fr;
  }
}
